#!/usr/bin/env python3
"""
Test script to verify signature functionality works correctly.
"""
import sys
import os
import base64
from io import BytesIO
from PIL import Image

# Add backend to path
sys.path.append('backend')

def create_test_signature():
    """Create a test signature image as base64."""
    # Create a simple signature-like image
    img = Image.new('RGB', (200, 100), color='white')
    
    # Draw a simple signature-like line
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img)
    
    # Draw a simple signature curve
    points = [(20, 50), (50, 30), (80, 60), (110, 40), (140, 55), (170, 45)]
    for i in range(len(points) - 1):
        draw.line([points[i], points[i + 1]], fill='black', width=2)
    
    # Convert to base64
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    img_bytes = buffer.getvalue()
    
    # Create data URL
    base64_string = base64.b64encode(img_bytes).decode('utf-8')
    data_url = f"data:image/png;base64,{base64_string}"
    
    return data_url

def test_image_utils():
    """Test the image utility functions."""
    print("🧪 Testing image utility functions...")
    
    try:
        from app.utils.image_utils import validate_signature_image, process_signature_base64
        
        # Create test signature
        test_signature = create_test_signature()
        print(f"✅ Created test signature: {len(test_signature)} characters")
        
        # Test validation
        is_valid = validate_signature_image(test_signature)
        print(f"✅ Signature validation: {'PASSED' if is_valid else 'FAILED'}")
        
        # Test processing
        try:
            image_bytes, content_type = process_signature_base64(test_signature)
            print(f"✅ Signature processing: PASSED ({len(image_bytes)} bytes, {content_type})")
        except Exception as e:
            print(f"❌ Signature processing: FAILED - {str(e)}")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

def test_template_service():
    """Test the template service signature processing."""
    print("\n🧪 Testing template service...")
    
    try:
        from app.services.document_template_service import DocumentTemplateService
        
        service = DocumentTemplateService()
        
        # Create test template data with signatures
        test_data = {
            'klant_naam': 'Test Customer',
            'klant_handtekening': create_test_signature(),
            'monteur_naam': 'Test Technician',
            'monteur_handtekening': create_test_signature(),
            'datum': '2024-01-15',
            'regular_field': 'Some regular data'
        }
        
        # Process the data
        processed_data = service.process_template_with_signatures(test_data)
        
        # Check results
        if 'klant_handtekening' in processed_data and processed_data['klant_handtekening']:
            print("✅ Customer signature processed successfully")
        else:
            print("❌ Customer signature processing failed")
            return False
            
        if 'monteur_handtekening' in processed_data and processed_data['monteur_handtekening']:
            print("✅ Technician signature processed successfully")
        else:
            print("❌ Technician signature processing failed")
            return False
            
        if processed_data['regular_field'] == 'Some regular data':
            print("✅ Regular fields preserved correctly")
        else:
            print("❌ Regular fields not preserved")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Template service test failed: {str(e)}")
        return False

def test_invalid_signatures():
    """Test handling of invalid signature data."""
    print("\n🧪 Testing invalid signature handling...")
    
    try:
        from app.utils.image_utils import validate_signature_image
        from app.services.document_template_service import DocumentTemplateService
        
        service = DocumentTemplateService()
        
        # Test various invalid signatures
        invalid_signatures = [
            '',  # Empty string
            'not-a-signature',  # Plain text
            'data:image/png;base64,invalid-base64',  # Invalid base64
            'data:text/plain;base64,dGVzdA==',  # Wrong content type
        ]
        
        for i, invalid_sig in enumerate(invalid_signatures):
            is_valid = validate_signature_image(invalid_sig)
            if not is_valid:
                print(f"✅ Invalid signature {i+1} correctly rejected")
            else:
                print(f"❌ Invalid signature {i+1} incorrectly accepted")
                return False
        
        # Test processing with invalid signature
        test_data = {
            'klant_handtekening': 'invalid-signature-data',
            'monteur_handtekening': create_test_signature()  # Valid one
        }
        
        processed_data = service.process_template_with_signatures(test_data)
        
        # Invalid signature should be cleared
        if processed_data['klant_handtekening'] == '':
            print("✅ Invalid signature correctly cleared")
        else:
            print("❌ Invalid signature not cleared")
            return False
            
        # Valid signature should be preserved
        if processed_data['monteur_handtekening']:
            print("✅ Valid signature preserved")
        else:
            print("❌ Valid signature not preserved")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Invalid signature test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting signature functionality tests...\n")
    
    # Change to backend directory for imports
    os.chdir('backend')
    
    tests = [
        test_image_utils,
        test_template_service,
        test_invalid_signatures
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()  # Add spacing between tests
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Signature functionality is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
